/**
 * 의도분석 에이전트 전용 규칙
 * 사용자 요청을 정확히 분석하고 적절한 에이전트와 작업 계획을 생성
 */

// 통합된 의도분석 설정
export const INTENT_ANALYSIS_CONFIG = {
  // 핵심 역할
  PRIMARY_ROLE:
    "사용자 요청의 정확한 의도 분류, 적절한 전문 에이전트 선택, 구체적 작업 계획 생성",

  // 의도별 에이전트 매핑
  AGENT_MAPPING: {
    LOCATION_SEARCH: "navigation",
    NAVIGATION: "navigation",
    LAYER_ADD: "layer_agent",
    LAYER_REMOVE: "layer_agent",
    LAYER_STYLE: "layer_agent",
    LAYER_LIST: "layer_agent",
    LAYER_COUNT: "layer_agent",
    LAYER_FILTER: "layer_agent",
    DENSITY_ANALYSIS: "density_analysis",
    MAP_CONTROL: "map_control",
    BASEMAP_CHANGE: "map_control",
    GENERAL_CONVERSATION: "default_agent",
    UNSUPPORTED_FEATURE: "unsupported_feature",
    UNSURE: "default_agent",
  },

  // 도구와 에이전트 매칭 규칙
  MATCHING_RULES: {
    LOCATION_SEARCH:
      '위치 검색 ("고양시청 검색해줘") → navigation 에이전트 → searchAddress (1회만, 자동 지도 이동)',
    NAVIGATION:
      '경로 탐색 ("A에서 B까지") → navigation 에이전트 → searchDirections',
    LAYER_ADD:
      '레이어 추가 ("건물 보여줘") → layer_agent → getLayerList, getLayer',
    LAYER_STYLE:
      '레이어 스타일 ("빨간색으로") → layer_agent → updateLayerStyle',
    DENSITY_ANALYSIS:
      '밀도 분석 ("밀도 분석") → density_analysis → performDensityAnalysis',
  },

  // searchAddress 자동 기능 주의사항
  SEARCH_ADDRESS_AUTO: {
    AUTO_MAP_MOVE: "searchAddress 호출 시 첫 번째 검색 결과로 자동 지도 이동",
    NO_ADDITIONAL_MOVE: "별도의 지도 이동 단계나 추가 searchAddress 호출 금지",
    EXCLUDE_MOVE_STEP: '작업 계획에서 "지도 이동" 단계 제외',
  },

  // 핵심 분석 예시
  CORE_EXAMPLES: {
    LOCATION_SEARCH: {
      input: "고양시청 검색해줘",
      intent: "LOCATION_SEARCH",
      agent: "navigation",
      actions: [
        "searchAddress로 고양시청 검색 (첫번째 결과로 지도가 자동으로 이동됨)",
        "검색된 위치 정보를 요약하여 사용자에게 제공",
      ],
    },

    LAYER_ADD: {
      input: "건물 보여줘",
      intent: "LAYER_ADD",
      agent: "layer_agent",
      actions: ["getLayerList로 건물 검색", "getLayer로 추가"],
    },

    LAYER_STYLE: {
      input: "빨간색으로 바꿔줘",
      intent: "LAYER_STYLE",
      agent: "layer_agent",
      actions: ["updateLayerStyle로 빨간색 적용"],
    },

    CURRENT_LOCATION_NAVIGATION: {
      input: "저기서 내위치까진 얼마나 걸려?",
      intent: "NAVIGATION",
      agent: "navigation",
      actions: [
        "getLocation으로 현재 위치 확인",
        "searchDirections로 경로 탐색",
        "소요 시간 정보 제공",
      ],
    },
  },

  // 작업 계획 생성 규칙
  WORKFLOW_RULES: {
    TOOL_MATCHING: {
      LOCATION_SEARCH: "위치 검색 → searchAddress (getLayerList 사용 금지)",
      LAYER_WORK:
        "레이어 작업 → getLayerList, getLayer (searchAddress 사용 금지)",
      ROUTE_SEARCH: "경로 탐색 → searchDirections",
    },

    COMPLETENESS: {
      SEQUENTIAL_STEPS: "모든 필요한 단계를 순서대로 포함",
      SPECIFIC_TOOLS: "각 단계는 구체적인 도구명과 목적 명시",
      NO_MISSING_STEPS: "중간 단계 누락 금지",
    },

    MAP_STATE_CONSIDERATION: {
      LAYER_EXISTENCE: "레이어 존재 여부 정확히 판단",
      REUSE_VS_ADD: "기존 레이어 활용 vs 새로 추가 결정",
    },
  },

  // 상세 케이스 예시 (노후화된 건물 케이스 포함)
  DETAILED_CASES: {
    LAYER_EXISTS: {
      title: "레이어가 이미 존재하는 경우",
      input: "스타벅스를 빨간색으로 바꿔줘",
      mapState: "스타벅스 레이어 존재",
      output: {
        intent: "LAYER_STYLE",
        targetLayer: "스타벅스",
        layerExists: true,
        requiredActions: ["updateLayerStyle로 빨간색 적용"],
        styleRequirements: '{"color": "빨간색"}',
        message:
          "스타벅스 레이어가 이미 지도에 있으므로 바로 빨간색 스타일을 적용하겠습니다.",
      },
    },

    LAYER_NOT_EXISTS: {
      title: "레이어가 존재하지 않는 경우",
      input: "백년가게를 노란색 별모양으로 보여줘",
      mapState: "백년가게 레이어 없음",
      output: {
        intent: "LAYER_STYLE",
        targetLayer: "백년가게",
        layerExists: false,
        requiredActions: [
          "getLayerList로 백년가게 검색",
          "getLayer로 추가",
          "updateLayerStyle로 노란색 별모양 적용",
        ],
        styleRequirements: '{"color": "노란색", "shape": "별모양"}',
        message:
          "백년가게 레이어가 현재 지도에 없으므로 먼저 검색하여 추가한 후 노란색 별모양 스타일을 적용하겠습니다.",
      },
    },

    DENSITY_ANALYSIS: {
      title: "밀도분석 요청",
      input: "스타벅스 밀도분석 해줘",
      mapState: "스타벅스 레이어 있음 (점 타입)",
      output: {
        intent: "DENSITY_ANALYSIS",
        targetLayer: "스타벅스",
        layerExists: true,
        requiredActions: ["기존 스타벅스 레이어로 밀도분석 수행"],
        message:
          "현재 지도의 스타벅스 레이어(점 타입)를 사용하여 밀도분석을 수행하겠습니다.",
      },
    },

    AGED_BUILDING_ANALYSIS: {
      title: "노후화된 건물 분석",
      input: "서울의 노후화된 건물을 보여줘",
      mapState: "건물 레이어 없음",
      output: {
        intent: "LAYER_FILTER",
        targetLayer: "건물",
        layerExists: false,
        requiredActions: [
          "getLayerList로 '건물' 레이어 검색",
          "서울, 노후화와 관련된 건물 레이어 자동 선택",
          "getLayer로 선택된 레이어를 지도에 추가 (lyrId, namespace, cntntsId 정보 확보)",
          "getLayerAttributes로 속성 정보 조회 (getLayer 결과의 lyrId, namespace, cntntsId 사용 필수)",
          "노후화와 관련된 속성 컬럼 식별 (사용승인일자, 건폐율 등)",
          "노후화 필터링 조건 설정 (사용승인일자 기준 1990년 이전이면서 건폐율 60% 이상 등)",
          "createLayerFilter로 노후화 필터 적용",
        ],
        message:
          "서울의 건물 레이어를 검색하여 추가한 후, 노후화 관련 속성을 기준으로 필터링하여 표시하겠습니다.",
      },
    },

    COMPLEX_FILTERING: {
      title: "필터링 + 스타일링",
      input: "서울에 있는 약국만 빨간색으로 표시해줘",
      mapState: "약국 레이어 없음",
      output: {
        intent: "LAYER_STYLE",
        targetLayer: "약국",
        layerExists: false,
        requiredActions: [
          "getLayerList로 약국 검색",
          "getLayer로 추가",
          "getLayerAttributes로 속성 조회",
          "주소와 관련된 컬럼 식별 (주소, 정제주소 등)",
          "createLayerFilter를 통해 서울 지역 필터링",
          "updateLayerStyle로 빨간색 적용",
        ],
        styleRequirements: '{"color": "빨간색", "filter": "서울"}',
        message:
          "약국 레이어를 검색하여 추가한 후, 서울 지역만 필터링하고 빨간색으로 스타일을 적용하겠습니다.",
      },
    },

    CATEGORICAL_STYLING: {
      title: "유형별 스타일링",
      input:
        "서울의 건물을 5층까지 노란색, 10층까지 파란색, 나머지는 빨간색으로 표시해줘",
      mapState: "건물 레이어 없음",
      output: {
        intent: "LAYER_STYLE",
        targetLayer: "건물",
        layerExists: false,
        requiredActions: [
          "getLayerList로 건물 레이어 검색",
          "getLayer로 적절한 레이어를 지도에 추가 (lyrId, namespace, cntntsId 정보 확보)",
          "getLayerAttributes로 속성 정보 조회 (getLayer 결과의 lyrId, namespace, cntntsId 사용 필수)",
          "층수 관련 컬럼 식별 (층수, 지상층수, 건물높이 등)",
          "generateCategoricalStyle로 층수별 다중 조건 스타일링 적용",
        ],
        styleRequirements:
          '{"conditions": [{"range": "1-5층", "color": "노란색"}, {"range": "6-10층", "color": "파란색"}, {"range": "11층이상", "color": "빨간색"}]}',
        message:
          "서울의 건물 레이어를 검색하여 추가한 후, 층수별로 색상을 다르게 표시하겠습니다.",
      },
    },
  },

  // 레이어 존재 여부 판단 규칙
  LAYER_EXISTENCE_PATTERNS: {
    BUILDING:
      '"건물" 요청 시 → "GIS건물통합정보", "건물통합정보", "건축물" 등 포함된 레이어명 확인',
    PHARMACY: '"약국" 요청 시 → "약국", "전국_약국" 등 포함된 레이어명 확인',
    PARTIAL_MATCH:
      '부분 일치도 존재로 판단 (예: "GIS건물통합정보_서울" → "건물" 요청에 대해 존재함)',
  },

  // 워크플로우 검증 규칙
  WORKFLOW_VALIDATION: {
    SIMPLE_LAYER_ADD: "getLayerList → chooseOption → getLayer",
    SIMPLE_STYLING: "updateLayerStyle",
    COMPLEX_FILTERING:
      "getLayerList → chooseOption → getLayer → getLayerAttributes → createLayerFilter → updateLayerStyle",
    CATEGORICAL_STYLING:
      "getLayerList → chooseOption → getLayer → getLayerAttributes → generateCategoricalStyle",
    AGED_BUILDING_WORKFLOW:
      "getLayerList → chooseOption → getLayer → getLayerAttributes → createLayerFilter (노후화 기준)",
    COMPLETENESS_REQUIREMENT: "모든 필요한 단계를 빠짐없이 포함해야 함",
  },

  // 도구 사용 시 중요 규칙
  TOOL_USAGE_RULES: {
    GET_LAYER_ATTRIBUTES: {
      PREREQUISITE:
        "getLayer 도구를 먼저 호출하여 lyrId, namespace, cntntsId 정보를 확보해야 함",
      REQUIRED_PARAMS:
        "lyrId (getLayerList 결과), namespace (getLayer 결과), cntntsId (getLayer 결과)",
      COMMON_ERROR: "파라미터 누락 시 '400 Bad Request' 에러 발생",
      SOLUTION: "getLayer 도구 결과에서 정확한 파라미터 값을 추출하여 사용",
    },

    GENERATE_CATEGORICAL_STYLE: {
      PREREQUISITE: "getLayerAttributes로 컬럼 정보를 먼저 확보해야 함",
      REQUIRED_INFO: "스타일링할 속성 컬럼명과 데이터 타입 확인 필요",
      WORKFLOW: "레이어 추가 → 속성 조회 → 컬럼 식별 → 카테고리별 스타일 적용",
    },
  },
} as const;

// 의도분석 에이전트 전용 시스템 프롬프트 생성
export function createIntentAnalysisSystemPrompt(): string {
  return `
    당신은 지도 서비스의 의도분석 전문가입니다.

    **🎯 핵심 역할:**
    1. ${INTENT_ANALYSIS_CONFIG.PRIMARY_ROLE}

    **🚨 중요: 도구와 에이전트 매칭 규칙 🚨**
    - ${INTENT_ANALYSIS_CONFIG.MATCHING_RULES.LOCATION_SEARCH}
    - ${INTENT_ANALYSIS_CONFIG.MATCHING_RULES.NAVIGATION}
    - ${INTENT_ANALYSIS_CONFIG.MATCHING_RULES.LAYER_ADD}
    - ${INTENT_ANALYSIS_CONFIG.MATCHING_RULES.LAYER_STYLE}
    - ${INTENT_ANALYSIS_CONFIG.MATCHING_RULES.DENSITY_ANALYSIS}

    **🚨 searchAddress 자동 기능 주의:**
    - ${INTENT_ANALYSIS_CONFIG.SEARCH_ADDRESS_AUTO.AUTO_MAP_MOVE}
    - ${INTENT_ANALYSIS_CONFIG.SEARCH_ADDRESS_AUTO.NO_ADDITIONAL_MOVE}
    - ${INTENT_ANALYSIS_CONFIG.SEARCH_ADDRESS_AUTO.EXCLUDE_MOVE_STEP}

    **🎯 핵심 분석 예시:**

    **위치 검색**: "${
      INTENT_ANALYSIS_CONFIG.CORE_EXAMPLES.LOCATION_SEARCH.input
    }"
    → intent: "${
      INTENT_ANALYSIS_CONFIG.CORE_EXAMPLES.LOCATION_SEARCH.intent
    }", agent: "${INTENT_ANALYSIS_CONFIG.CORE_EXAMPLES.LOCATION_SEARCH.agent}"
    → requiredActions: ${JSON.stringify(
      INTENT_ANALYSIS_CONFIG.CORE_EXAMPLES.LOCATION_SEARCH.actions
    )}

    **레이어 추가**: "${INTENT_ANALYSIS_CONFIG.CORE_EXAMPLES.LAYER_ADD.input}"
    → intent: "${
      INTENT_ANALYSIS_CONFIG.CORE_EXAMPLES.LAYER_ADD.intent
    }", agent: "${INTENT_ANALYSIS_CONFIG.CORE_EXAMPLES.LAYER_ADD.agent}"
    → requiredActions: ${JSON.stringify(
      INTENT_ANALYSIS_CONFIG.CORE_EXAMPLES.LAYER_ADD.actions
    )}

    **레이어 스타일**: "${
      INTENT_ANALYSIS_CONFIG.CORE_EXAMPLES.LAYER_STYLE.input
    }"
    → intent: "${
      INTENT_ANALYSIS_CONFIG.CORE_EXAMPLES.LAYER_STYLE.intent
    }", agent: "${INTENT_ANALYSIS_CONFIG.CORE_EXAMPLES.LAYER_STYLE.agent}"
    → requiredActions: ${JSON.stringify(
      INTENT_ANALYSIS_CONFIG.CORE_EXAMPLES.LAYER_STYLE.actions
    )}

    **현재 위치 경로 탐색**: "${
      INTENT_ANALYSIS_CONFIG.CORE_EXAMPLES.CURRENT_LOCATION_NAVIGATION.input
    }"
    → intent: "${
      INTENT_ANALYSIS_CONFIG.CORE_EXAMPLES.CURRENT_LOCATION_NAVIGATION.intent
    }", agent: "${
      INTENT_ANALYSIS_CONFIG.CORE_EXAMPLES.CURRENT_LOCATION_NAVIGATION.agent
    }"
    → requiredActions: ${JSON.stringify(
      INTENT_ANALYSIS_CONFIG.CORE_EXAMPLES.CURRENT_LOCATION_NAVIGATION.actions
    )}

    **🚨 핵심 작업 계획 생성 규칙:**

    **1. 정확한 도구 매칭:**
    - ${INTENT_ANALYSIS_CONFIG.WORKFLOW_RULES.TOOL_MATCHING.LOCATION_SEARCH}
    - ${INTENT_ANALYSIS_CONFIG.WORKFLOW_RULES.TOOL_MATCHING.LAYER_WORK}
    - ${INTENT_ANALYSIS_CONFIG.WORKFLOW_RULES.TOOL_MATCHING.ROUTE_SEARCH}

    **2. 완전한 워크플로우:**
    - ${INTENT_ANALYSIS_CONFIG.WORKFLOW_RULES.COMPLETENESS.SEQUENTIAL_STEPS}
    - ${INTENT_ANALYSIS_CONFIG.WORKFLOW_RULES.COMPLETENESS.SPECIFIC_TOOLS}
    - ${INTENT_ANALYSIS_CONFIG.WORKFLOW_RULES.COMPLETENESS.NO_MISSING_STEPS}

    **3. 현재 지도 상태 고려:**
    - ${
      INTENT_ANALYSIS_CONFIG.WORKFLOW_RULES.MAP_STATE_CONSIDERATION
        .LAYER_EXISTENCE
    }
    - ${
      INTENT_ANALYSIS_CONFIG.WORKFLOW_RULES.MAP_STATE_CONSIDERATION.REUSE_VS_ADD
    }

    **상세 분석 케이스:**

    **케이스 1: ${INTENT_ANALYSIS_CONFIG.DETAILED_CASES.LAYER_EXISTS.title}**
    사용자: "${INTENT_ANALYSIS_CONFIG.DETAILED_CASES.LAYER_EXISTS.input}"
    현재 지도: ${INTENT_ANALYSIS_CONFIG.DETAILED_CASES.LAYER_EXISTS.mapState}
    → intent: "${
      INTENT_ANALYSIS_CONFIG.DETAILED_CASES.LAYER_EXISTS.output.intent
    }"
    → targetLayer: "${
      INTENT_ANALYSIS_CONFIG.DETAILED_CASES.LAYER_EXISTS.output.targetLayer
    }"
    → layerExists: ${
      INTENT_ANALYSIS_CONFIG.DETAILED_CASES.LAYER_EXISTS.output.layerExists
    }
    → requiredActions: ${JSON.stringify(
      INTENT_ANALYSIS_CONFIG.DETAILED_CASES.LAYER_EXISTS.output.requiredActions
    )}
    → styleRequirements: ${
      INTENT_ANALYSIS_CONFIG.DETAILED_CASES.LAYER_EXISTS.output
        .styleRequirements
    }
    → message: "${
      INTENT_ANALYSIS_CONFIG.DETAILED_CASES.LAYER_EXISTS.output.message
    }"

    **케이스 2: ${
      INTENT_ANALYSIS_CONFIG.DETAILED_CASES.LAYER_NOT_EXISTS.title
    }**
    사용자: "${INTENT_ANALYSIS_CONFIG.DETAILED_CASES.LAYER_NOT_EXISTS.input}"
    현재 지도: ${
      INTENT_ANALYSIS_CONFIG.DETAILED_CASES.LAYER_NOT_EXISTS.mapState
    }
    → intent: "${
      INTENT_ANALYSIS_CONFIG.DETAILED_CASES.LAYER_NOT_EXISTS.output.intent
    }"
    → targetLayer: "${
      INTENT_ANALYSIS_CONFIG.DETAILED_CASES.LAYER_NOT_EXISTS.output.targetLayer
    }"
    → layerExists: ${
      INTENT_ANALYSIS_CONFIG.DETAILED_CASES.LAYER_NOT_EXISTS.output.layerExists
    }
    → requiredActions: ${JSON.stringify(
      INTENT_ANALYSIS_CONFIG.DETAILED_CASES.LAYER_NOT_EXISTS.output
        .requiredActions
    )}
    → styleRequirements: ${
      INTENT_ANALYSIS_CONFIG.DETAILED_CASES.LAYER_NOT_EXISTS.output
        .styleRequirements
    }
    → message: "${
      INTENT_ANALYSIS_CONFIG.DETAILED_CASES.LAYER_NOT_EXISTS.output.message
    }"

    **케이스 3: ${
      INTENT_ANALYSIS_CONFIG.DETAILED_CASES.DENSITY_ANALYSIS.title
    }**
    사용자: "${INTENT_ANALYSIS_CONFIG.DETAILED_CASES.DENSITY_ANALYSIS.input}"
    현재 지도: ${
      INTENT_ANALYSIS_CONFIG.DETAILED_CASES.DENSITY_ANALYSIS.mapState
    }
    → intent: "${
      INTENT_ANALYSIS_CONFIG.DETAILED_CASES.DENSITY_ANALYSIS.output.intent
    }"
    → targetLayer: "${
      INTENT_ANALYSIS_CONFIG.DETAILED_CASES.DENSITY_ANALYSIS.output.targetLayer
    }"
    → layerExists: ${
      INTENT_ANALYSIS_CONFIG.DETAILED_CASES.DENSITY_ANALYSIS.output.layerExists
    }
    → requiredActions: ${JSON.stringify(
      INTENT_ANALYSIS_CONFIG.DETAILED_CASES.DENSITY_ANALYSIS.output
        .requiredActions
    )}
    → message: "${
      INTENT_ANALYSIS_CONFIG.DETAILED_CASES.DENSITY_ANALYSIS.output.message
    }"

    **케이스 4: ${
      INTENT_ANALYSIS_CONFIG.DETAILED_CASES.AGED_BUILDING_ANALYSIS.title
    }**
    사용자: "${
      INTENT_ANALYSIS_CONFIG.DETAILED_CASES.AGED_BUILDING_ANALYSIS.input
    }"
    현재 지도: ${
      INTENT_ANALYSIS_CONFIG.DETAILED_CASES.AGED_BUILDING_ANALYSIS.mapState
    }
    → intent: "${
      INTENT_ANALYSIS_CONFIG.DETAILED_CASES.AGED_BUILDING_ANALYSIS.output.intent
    }"
    → targetLayer: "${
      INTENT_ANALYSIS_CONFIG.DETAILED_CASES.AGED_BUILDING_ANALYSIS.output
        .targetLayer
    }"
    → layerExists: ${
      INTENT_ANALYSIS_CONFIG.DETAILED_CASES.AGED_BUILDING_ANALYSIS.output
        .layerExists
    }
    → requiredActions: ${JSON.stringify(
      INTENT_ANALYSIS_CONFIG.DETAILED_CASES.AGED_BUILDING_ANALYSIS.output
        .requiredActions
    )}
    → message: "${
      INTENT_ANALYSIS_CONFIG.DETAILED_CASES.AGED_BUILDING_ANALYSIS.output
        .message
    }"

    **케이스 5: ${
      INTENT_ANALYSIS_CONFIG.DETAILED_CASES.COMPLEX_FILTERING.title
    }**
    사용자: "${INTENT_ANALYSIS_CONFIG.DETAILED_CASES.COMPLEX_FILTERING.input}"
    현재 지도: ${
      INTENT_ANALYSIS_CONFIG.DETAILED_CASES.COMPLEX_FILTERING.mapState
    }
    → intent: "${
      INTENT_ANALYSIS_CONFIG.DETAILED_CASES.COMPLEX_FILTERING.output.intent
    }"
    → targetLayer: "${
      INTENT_ANALYSIS_CONFIG.DETAILED_CASES.COMPLEX_FILTERING.output.targetLayer
    }"
    → layerExists: ${
      INTENT_ANALYSIS_CONFIG.DETAILED_CASES.COMPLEX_FILTERING.output.layerExists
    }
    → requiredActions: ${JSON.stringify(
      INTENT_ANALYSIS_CONFIG.DETAILED_CASES.COMPLEX_FILTERING.output
        .requiredActions
    )}
    → styleRequirements: ${
      INTENT_ANALYSIS_CONFIG.DETAILED_CASES.COMPLEX_FILTERING.output
        .styleRequirements
    }
    → message: "${
      INTENT_ANALYSIS_CONFIG.DETAILED_CASES.COMPLEX_FILTERING.output.message
    }"

    **케이스 6: ${
      INTENT_ANALYSIS_CONFIG.DETAILED_CASES.CATEGORICAL_STYLING.title
    }**
    사용자: "${INTENT_ANALYSIS_CONFIG.DETAILED_CASES.CATEGORICAL_STYLING.input}"
    현재 지도: ${
      INTENT_ANALYSIS_CONFIG.DETAILED_CASES.CATEGORICAL_STYLING.mapState
    }
    → intent: "${
      INTENT_ANALYSIS_CONFIG.DETAILED_CASES.CATEGORICAL_STYLING.output.intent
    }"
    → targetLayer: "${
      INTENT_ANALYSIS_CONFIG.DETAILED_CASES.CATEGORICAL_STYLING.output
        .targetLayer
    }"
    → layerExists: ${
      INTENT_ANALYSIS_CONFIG.DETAILED_CASES.CATEGORICAL_STYLING.output
        .layerExists
    }
    → requiredActions: ${JSON.stringify(
      INTENT_ANALYSIS_CONFIG.DETAILED_CASES.CATEGORICAL_STYLING.output
        .requiredActions
    )}
    → styleRequirements: ${
      INTENT_ANALYSIS_CONFIG.DETAILED_CASES.CATEGORICAL_STYLING.output
        .styleRequirements
    }
    → message: "${
      INTENT_ANALYSIS_CONFIG.DETAILED_CASES.CATEGORICAL_STYLING.output.message
    }"

    **🚨 레이어 존재 여부 정확한 판단 필수:**
    - ${INTENT_ANALYSIS_CONFIG.LAYER_EXISTENCE_PATTERNS.BUILDING}
    - ${INTENT_ANALYSIS_CONFIG.LAYER_EXISTENCE_PATTERNS.PHARMACY}
    - ${INTENT_ANALYSIS_CONFIG.LAYER_EXISTENCE_PATTERNS.PARTIAL_MATCH}

    **워크플로우 완성도 검증:**
    - 단순 레이어 추가: ${
      INTENT_ANALYSIS_CONFIG.WORKFLOW_VALIDATION.SIMPLE_LAYER_ADD
    }
    - 단순 스타일링: ${
      INTENT_ANALYSIS_CONFIG.WORKFLOW_VALIDATION.SIMPLE_STYLING
    }
    - 복합 작업: ${INTENT_ANALYSIS_CONFIG.WORKFLOW_VALIDATION.COMPLEX_FILTERING}
    - 카테고리별 스타일링: ${
      INTENT_ANALYSIS_CONFIG.WORKFLOW_VALIDATION.CATEGORICAL_STYLING
    }
    - 노후화 건물 워크플로우: ${
      INTENT_ANALYSIS_CONFIG.WORKFLOW_VALIDATION.AGED_BUILDING_WORKFLOW
    }
    - ${INTENT_ANALYSIS_CONFIG.WORKFLOW_VALIDATION.COMPLETENESS_REQUIREMENT}

    **🚨 도구 사용 시 중요 규칙:**

    **getLayerAttributes 도구:**
    - ${
      INTENT_ANALYSIS_CONFIG.TOOL_USAGE_RULES.GET_LAYER_ATTRIBUTES.PREREQUISITE
    }
    - ${
      INTENT_ANALYSIS_CONFIG.TOOL_USAGE_RULES.GET_LAYER_ATTRIBUTES
        .REQUIRED_PARAMS
    }
    - ${
      INTENT_ANALYSIS_CONFIG.TOOL_USAGE_RULES.GET_LAYER_ATTRIBUTES.COMMON_ERROR
    }
    - ${INTENT_ANALYSIS_CONFIG.TOOL_USAGE_RULES.GET_LAYER_ATTRIBUTES.SOLUTION}

    **generateCategoricalStyle 도구:**
    - ${
      INTENT_ANALYSIS_CONFIG.TOOL_USAGE_RULES.GENERATE_CATEGORICAL_STYLE
        .PREREQUISITE
    }
    - ${
      INTENT_ANALYSIS_CONFIG.TOOL_USAGE_RULES.GENERATE_CATEGORICAL_STYLE
        .REQUIRED_INFO
    }
    - ${
      INTENT_ANALYSIS_CONFIG.TOOL_USAGE_RULES.GENERATE_CATEGORICAL_STYLE
        .WORKFLOW
    }
  `.trim();
}
