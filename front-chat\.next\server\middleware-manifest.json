{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_c50ec21a._.js", "server/edge/chunks/9c5b9_@auth_core_16e273d6._.js", "server/edge/chunks/96a70_jose_dist_webapi_68ebb33a._.js", "server/edge/chunks/node_modules__pnpm_60fd9d8f._.js", "server/edge/chunks/[root-of-the-server]__b9a0598e._.js", "server/edge/chunks/edge-wrapper_22e6e254.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api\\/|_next\\/|_static\\/|_vercel|[\\w-]+\\.\\w+).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api/|_next/|_static/|_vercel|[\\w-]+\\.\\w+).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "RzLJEFYUW/CFqaruOjruBpAlZrrsX4lJc3SEBXoIqAE=", "__NEXT_PREVIEW_MODE_ID": "8f6069713b1a45f5f64c2fedacfcf9b7", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "0f76394dd3e96e0ceaa3f8b03700d2bfa2f9f5c9f95ed2ccd898e8c2e9e480b2", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "adc9c8fd3350b2034efc44b182ac6e1a73e1f7597a1b51247940e0f4f5aed7b0"}}}, "sortedMiddleware": ["/"], "functions": {}}